const parse = require('@babel/parser')
const traverse = require('@babel/traverse').default
//检测节点类型
const types = require('@babel/types')
// 将ast还原成JavaScript代码
const generator = require("@babel/generator").default;

jscode = `var b = 1 + 2;
var c = "coo" + "kie";
var a = 1+1,b = 2+2;
var c = 3;
var d = "1" + 1;
var e = 1 + '2';
`
// 解析js代码为ast树结构
var ast = parse.parse(jscode);
traverse(ast, {
    BinaryExpression(path) {
        //获取到当前节点内容
        var {left, operator, right} = path.node;
        // console.log(left, operator, right);
        if (types.isNumericLiteral(left) && types.isNumericLiteral(right) && operator == "+" || types.isStringLiteral(left) && types.isStringLiteral(right)) {
            var vaules = left.value + right.value
            path.re
        }
    }

})