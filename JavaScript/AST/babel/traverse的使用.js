// 解析js代码为ast树结构
const parse = require('@babel/parser')
// 遍历ast树结构
const traverse = require('@babel/traverse').default
// JS 转 ast语法树
jscode = `var a = "\u0068\u0065\u006c\u006c\u006f\u002c\u0041\u0053\u0054";`
// 转换js代码为ast树结构
let ast = parse.parse(jscode);

// 用查找定位节点(ast结构树, 访问器对象)
traverse(ast, {
	// 定位VariableDeclarator类别,path是定位之后的地址
    VariableDeclarator(path){
        //path.node是当前节点
        console.log('Found identifier:', path.node.init.value);
        //path.toString()是当前节点的源代码
        console.log( path.toString());
        //path.parentPath是父节点
        console.log( path.parentPath.node);
        //path.container是当前节点的所有兄弟节点
        console.log( path.container);
        //path.type是当前节点的类型
        console.log( path.type);
        //path.get('init')是获取当前节点的子节点
        console.log( path.get('init'));
    }
})
