import requests
import execjs


class YiBao:
    def __init__(self):
        self.headers = {
            "Accept": "application/json",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "application/json",
            "Origin": "https://fuwu.nhsa.gov.cn",
            "Pragma": "no-cache",
            "Referer": "https://fuwu.nhsa.gov.cn/nationalHallSt/",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "X-Tingyun": "c=B|4Nl_NnGbjwY;x=2e7fdc0444224dd8",
            "channel": "web",
            "contentType": "application/x-www-form-urlencoded",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "x-tif-nonce": "Y3nUsO94",
            "x-tif-paasid": "undefined",
            "x-tif-signature": "e8cf8e64d3fb13fbcfaee44b454936fcb1b9cc2841ae19fbeafb63fcb17bb3ba",
            "x-tif-timestamp": "**********"
        }
        self.url = "https://fuwu.nhsa.gov.cn/ebus/fuwu/api/nthl/api/CommQuery/queryFixedHospital"
        self.js_code = open('医保.js','r',encoding='utf-8').read()
        self.js = execjs.compile(self.js_code)


    def get_data(self, page):
        header = self.js.call('get_headers', page)
        self.headers.update(header)
        print(self.headers)
        # response = requests.post(self.url, headers=self.headers)
        # return response.json()

    def parse_data(self, response):
        pass

    def main(self):
        get_data()

if __name__ == '__main__':
    yb = YiBao()
    yb.main()
